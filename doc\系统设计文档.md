# BBS论坛系统设计文档

## 1. 项目概述

### 1.1 项目信息
- **项目名称**: BBS论坛系统
- **技术架构**: SpringBoot 3 + MySQL + Vue 3
- **开发语言**: Java 17
- **数据库**: MySQL 8.0+
- **构建工具**: Maven

### 1.2 核心功能
- 用户注册、登录、个人资料管理
- 帖子发布、回复、点赞
- 板块管理
- 积分系统
- 管理员功能

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │    │   后端 (Spring)  │    │   数据库 (MySQL) │
│                 │    │                 │    │                 │
│ - 用户界面       │◄──►│ - REST API      │◄──►│ - 用户数据       │
│ - 路由管理       │    │ - 业务逻辑       │    │ - 帖子数据       │
│ - 状态管理       │    │ - 数据访问       │    │ - 板块数据       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 后端分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Controller Layer                         │
│              (REST API 接口层)                              │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│                  (业务逻辑层)                                │
├─────────────────────────────────────────────────────────────┤
│                    Mapper Layer                             │
│                 (数据访问层)                                 │
├─────────────────────────────────────────────────────────────┤
│                    Entity Layer                             │
│                  (实体类层)                                  │
└─────────────────────────────────────────────────────────────┘
```

## 3. 技术选型

### 3.1 后端技术栈
- **Spring Boot 3.5.0**: 主框架
- **MyBatis Plus 3.5.12**: ORM框架
- **MySQL Connector**: 数据库驱动
- **JWT 0.12.5**: 身份认证
- **SpringDoc OpenAPI**: API文档

### 3.2 开发工具
- **Maven**: 项目构建
- **Git**: 版本控制

## 4. 数据库设计

### 4.1 核心表结构
- **user**: 用户表
- **section**: 板块表  
- **post**: 帖子表
- **comments**: 评论表

### 4.2 关系设计
- 用户与帖子：一对多
- 用户与评论：一对多
- 帖子与评论：一对多
- 板块与帖子：一对多

## 5. API设计

### 5.1 用户模块 (/api/users)
- POST /register - 用户注册
- POST /login - 用户登录
- GET /profile - 获取个人信息
- PUT /profile - 更新个人信息
- POST /checkin - 每日签到

### 5.2 帖子模块 (/api/posts)
- GET / - 获取帖子列表
- GET /{id} - 获取帖子详情
- POST / - 发布帖子
- PUT /{id} - 更新帖子
- DELETE /{id} - 删除帖子
- POST /{id}/like - 点赞帖子

### 5.3 评论模块 (/api/comments)
- GET /post/{postId} - 获取帖子评论
- POST / - 发布评论
- PUT /{id} - 更新评论
- DELETE /{id} - 删除评论

### 5.4 板块模块 (/api/sections)
- GET / - 获取板块列表
- GET /{id} - 获取板块详情
- POST / - 创建板块 (管理员)
- PUT /{id} - 更新板块 (管理员)
- DELETE /{id} - 删除板块 (管理员)

### 5.5 管理员模块 (/api/admin)
- PUT /posts/{id}/pin - 置顶帖子
- PUT /posts/{id}/feature - 加精帖子
- PUT /users/{id}/points - 调整用户积分

## 6. 安全设计

### 6.1 认证机制
- JWT Token认证
- Token过期时间：24小时
- 刷新Token机制

### 6.2 权限控制
- 游客：只读权限
- 普通用户：发帖、回复、点赞
- 管理员：所有权限

### 6.3 数据安全
- 密码BCrypt加密
- SQL注入防护
- XSS攻击防护

## 7. 性能优化

### 7.1 数据库优化
- 索引设计
- 分页查询
- 连接池配置

### 7.2 缓存策略
- 热门帖子缓存
- 用户信息缓存
- 板块信息缓存

## 8. 部署方案

### 8.1 开发环境
- 本地MySQL数据库
- 内嵌Tomcat服务器
- 热部署支持

### 8.2 生产环境
- Docker容器化部署
- Nginx反向代理
- MySQL主从复制

## 9. 项目结构

```
src/main/java/cn/byssted/bbs/bbsrd/
├── config/          # 配置类
├── controller/      # 控制器
├── service/         # 服务层
├── mapper/          # 数据访问层
├── entity/          # 实体类
├── dto/             # 数据传输对象
├── vo/              # 视图对象
├── common/          # 通用类
├── exception/       # 异常处理
└── util/            # 工具类
```

## 10. 开发规范

### 10.1 命名规范
- 类名：大驼峰命名
- 方法名：小驼峰命名
- 常量：全大写下划线分隔
- 包名：全小写

### 10.2 代码规范
- 统一使用UTF-8编码
- 代码注释完整
- 异常处理规范
- 日志记录规范
