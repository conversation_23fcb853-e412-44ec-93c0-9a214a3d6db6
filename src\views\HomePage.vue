<template>
  <div class="common-layout">
    <el-container class="home-page-el-container">
      <el-header class="home-page-header">
        <el-menu :default-active="activeIndex" :ellipsis="false" class="el-menu-demo" mode="horizontal"
          @select="handleMenuSelect" text-color="#333" active-text-color="#ffd04b" router>
          <el-menu-item index="/home">
            <img style="height: 100px; width: auto; vertical-align: middle;" :src="logo" alt="校徽" />
          </el-menu-item>
          <el-menu-item index="/home/<USER>">1</el-menu-item>
          <el-menu-item index="/home/<USER>">2</el-menu-item>
          <el-menu-item index="/home/<USER>">3</el-menu-item>
          <!-- 你可以根据需要添加更多菜单项 -->
        </el-menu>
        <el-button-group class="header-actions">
          <el-button type="primary" @click="openLoginDialog">登录</el-button>
          <el-button type="primary" @click="openRegisterDialog">注册</el-button>
        </el-button-group>
      </el-header>
      <el-main class="home-page-main">
        <router-view />
      </el-main>
    </el-container>
  </div>

  <!-- 登录对话框 -->
  <el-dialog v-model="loginDialogVisible" :show-close="false" width="500" >
  <LoginForm 
    v-if="loginDialogVisible" 
    @close="closeLoginDialog"
    @switch-register="handleSwitchToRegister"
  />
</el-dialog>

  <!-- 注册对话框 -->
  <el-dialog v-model="registerDialogVisible" :show-close="false" width="500px">
    <RegisterForm 
      v-if="registerDialogVisible" 
      @close="closeRegisterDialog"
      @switch-login="handleSwitchToLogin"
    />
  </el-dialog>
</template>

<script setup>
import logo from '@/assets/校徽.svg';
import LoginForm from '@/components/LoginForm.vue'; // 引入登录表单组件
import RegisterForm from '@/components/RegisterForm.vue'; // 引入注册表单组件

import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// activeIndex 用于el-menu高亮当前路由对应的菜单项
const activeIndex = ref(route.path);

// 监听路由变化，确保菜单高亮状态与当前路由同步
watch(
  () => route.path,
  (newPath) => {
    activeIndex.value = newPath;
  }
);

// 控制登录对话框的显示状态
const loginDialogVisible = ref(false);
// 控制注册对话框的显示状态
const registerDialogVisible = ref(false);

const openLoginDialog = () => {
  loginDialogVisible.value = true;
};

const closeLoginDialog = () => {
  loginDialogVisible.value = false;
};

const handleCloseLoginDialog = (done) => {
  // 可选：在关闭前做一些清理工作或确认
  closeLoginDialog();
  done(); // 必须调用 done() 才能关闭对话框
};

const openRegisterDialog = () => {
  registerDialogVisible.value = true;
};

const closeRegisterDialog = () => {
  registerDialogVisible.value = false;
};

const handleSwitchToRegister = () => {
  closeLoginDialog()
  openRegisterDialog()
}

const handleSwitchToLogin = () => {
  closeRegisterDialog()
  openLoginDialog()
}


</script>

<style scoped>
.common-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.home-page-el-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.home-page-header {
  padding: 0;
  /* 移除el-header的默认padding，让el-menu更好地填充 */
  height: 100px;
  /* 设置header高度为100px */
  line-height: 100px;
  /* 确保内容在header中垂直居中 */
  background-color: #ffffff;
  /* 给Header一个背景色 */
  display: flex;
  align-items: center;

}

.el-menu-demo {
  --el-menu-horizontal-height: 100px;
  /* 设置菜单项高度为100px */
  background-color: #ffffff;
  border-bottom: none;
}

.header-actions {
  margin-left: auto;
  margin-right: 20px; /* 与右边缘的间距 */
 }
.home-page-main {
  padding: 20px;
  flex-grow: 1;
  /* 让main区域占据剩余空间 */
  overflow-y: auto;
  /* 如果内容超出，则显示滚动条 */
}
</style>
