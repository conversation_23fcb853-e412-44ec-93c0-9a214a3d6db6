<template>
  <div class="register-container">
    <!-- 关闭按钮 -->
    <el-icon class="close-btn" @click="emit('close')"><Close /></el-icon>

    <!-- 主标题 -->
    <h1 class="register-title">注册</h1>

    <el-form :model="form" class="register-form">
      <!-- 用户名输入 -->
      <el-form-item>
        <el-input 
          v-model="form.username" 
          placeholder="请输入用户名"
          clearable
        >
          <template #prefix>
            <el-icon class="input-icon"><User /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 邮箱输入 -->
      <el-form-item>
        <el-input 
          v-model="form.email" 
          placeholder="请输入邮箱"
          clearable
        >
          <template #prefix>
            <el-icon class="input-icon"><Message /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 密码输入 -->
      <el-form-item>
        <el-input
          v-model="form.password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入密码"
        >
          <template #prefix>
            <el-icon class="input-icon"><Lock /></el-icon>
          </template>
          <template #suffix>
            <el-icon 
              class="password-eye"
              @click="showPassword = !showPassword"
            >
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 确认密码输入 -->
      <el-form-item>
        <el-input
          v-model="form.confirmPassword"
          :type="showConfirmPassword ? 'text' : 'password'"
          placeholder="请再次输入密码"
        >
          <template #prefix>
            <el-icon class="input-icon"><Lock /></el-icon>
          </template>
          <template #suffix>
            <el-icon 
              class="password-eye"
              @click="showConfirmPassword = !showConfirmPassword"
            >
              <View v-if="showConfirmPassword" />
              <Hide v-else />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 注册按钮 -->
      <el-button 
        type="primary" 
        class="submit-btn"
        @click="handleSubmit"
      >
        注册
      </el-button>

      <!-- 登录链接 -->
      <div class="login-link">
        <span>已有账号？</span>
        <el-link type="primary" @click="switchToLogin">立即登录</el-link>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { Close, User, Message, Lock, View, Hide } from '@element-plus/icons-vue'

const emit = defineEmits(['close', 'switch-login'])

// 表单数据
const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 密码可见状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)

const handleSubmit = () => {
  // 注册逻辑
  // 示例：检查密码是否一致
  if (form.password !== form.confirmPassword) {
    console.error('Passwords do not match!') // 替换为 Element Plus 提示
    return
  }
  console.log('submit register form:', form)
}

const switchToLogin = () => {
  emit('switch-login')
  emit('close') // 与LoginForm行为一致，先发切换事件，再发关闭事件
}
</script>

<style scoped>
.register-container {
  position: relative;
  width: 400px; /* 与LoginForm内容宽度一致 */
  padding: 40px;
  background: #fff;
  border-radius: 8px;
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #666;
}

.register-title {
  margin: 0 0 30px;
  font-size: 24px;
  color: #333;
  text-align: left;
}

.register-form {
  :deep(.el-input__wrapper) {
    height: 48px;
    padding: 0 15px;
    border-radius: 6px;
  }

  :deep(.el-input__prefix) {
    left: 12px;
    font-size: 18px;
  }

  :deep(.el-input__suffix) {
    right: 12px;
    cursor: pointer;
  }
}

.input-icon {
  color: #c0c4cc;
}

.password-eye {
  font-size: 18px;
  color: #999;
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  border-radius: 6px;
  margin-top: 10px; /* 调整与上方表单项的间距 */
}

.login-link {
  margin-top: 25px;
  text-align: center;
  font-size: 14px;
  
  .el-link {
    margin-left: 8px;
    vertical-align: baseline;
  }
}
</style>
  