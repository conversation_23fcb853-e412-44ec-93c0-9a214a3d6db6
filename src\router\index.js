import { createRouter, createWebHistory } from "vue-router";

import LoginPage from "@/views/LoginPage.vue";
import HomePage from "@/views/HomePage.vue";
import test from "@/views/test.vue";

const routes =[
    {path:'/', component: HomePage},
    {path:'/login', component: LoginPage},
    {path:'/test', component: test}
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export default router;