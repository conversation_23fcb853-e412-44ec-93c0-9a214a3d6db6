<template>
    <div class="login-container">
      <!-- 关闭按钮 -->
      <el-icon class="close-btn" @click="emit('close')"><Close /></el-icon>
      
      <!-- 主标题 -->
      <h1 class="login-title">登录</h1>
  
      <el-form :model="form" class="login-form">
        <!-- 邮箱输入 -->
        <el-form-item>
          <el-input 
            v-model="form.email" 
            placeholder="请输入邮箱"
            clearable
          >
            <template #prefix>
              <el-icon class="input-icon"><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
  
        <!-- 密码输入 -->
        <el-form-item>
          <el-input
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入密码"
          >
            <template #prefix>
              <el-icon class="input-icon"><Lock /></el-icon>
            </template>
            <template #suffix>
              <el-icon 
                class="password-eye"
                @click="showPassword = !showPassword"
              >
                <View v-if="showPassword" />
                <Hide v-else />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
  
        <!-- 操作区域 -->
        <div class="action-area">
          <el-checkbox v-model="form.remember">记住我</el-checkbox>
          <el-link type="primary" class="forgot-link">忘记密码？</el-link>
        </div>
  
        <!-- 登录按钮 -->
        <el-button 
          type="primary" 
          class="submit-btn"
          @click="handleSubmit"
        >
          登录
        </el-button>
  
        <!-- 注册链接 -->
        <div class="register-link">
          <span>没有账号？</span>
          <el-link type="primary" @click="switchToRegister">立即注册</el-link>
        </div>
      </el-form>
    </div>
  </template>
  
  <script setup>
  import { reactive, ref } from 'vue'
  import { Close, Message, Lock, View, Hide } from '@element-plus/icons-vue'
  
  const emit = defineEmits(['close', 'switch-register'])
  
  // 表单数据
  const form = reactive({
    email: '',
    password: '',
    remember: false
  })
  
  // 密码可见状态
  const showPassword = ref(false)
  
  const handleSubmit = () => {
    // 登录逻辑
    console.log('submit form:', form)
  }
  
  const switchToRegister = () => {
    emit('switch-register')
    emit('close')
  }
  </script>
  
  <style scoped>
  .login-container {
    position: relative;
    width: 400px;
    padding: 40px;
    background: #fff;
    border-radius: 8px;
  }
  
  .close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s;
  }
  
  .close-btn:hover {
    color: #666;
  }
  
  .login-title {
    margin: 0 0 30px;
    font-size: 24px;
    color: #333;
    text-align: left;
  }
  
  .login-form {
    :deep(.el-input__wrapper) {
      height: 48px;
      padding: 0 15px;
      border-radius: 6px;
    }
  
    :deep(.el-input__prefix) {
      left: 12px;
      font-size: 18px;
    }
  
    :deep(.el-input__suffix) {
      right: 12px;
      cursor: pointer;
    }
  }
  
  .input-icon {
    color: #c0c4cc;
  }
  
  .password-eye {
    font-size: 18px;
    color: #999;
  }
  
  .action-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 30px;
  }
  
  .forgot-link {
    font-size: 14px;
  }
  
  .submit-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    border-radius: 6px;
  }
  
  .register-link {
    margin-top: 25px;
    text-align: center;
    font-size: 14px;
    
    .el-link {
      margin-left: 8px;
      vertical-align: baseline;
    }
  }
  </style>